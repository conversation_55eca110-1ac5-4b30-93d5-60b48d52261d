package org.rc.platform.testagent.chat.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 大模型对话请求DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "大模型对话请求")
public class ChatAddReqDTO {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId = "1222477271620092911";

    /**
     * 会话Id，如果不输入，则创建新会话。
     */
    @Schema(description = "会话Id，如果不输入，则创建新会话")
    private String conversationId;

    /**
     * 应用Id
     */
    @Schema(description = "应用Id")
    private String appId;

    /**
     * 模型类型
     */
    @Schema(description = "模型类型")
    private String modelType = "deepseek_r1_32b";

    /**
     * 本次对话内容
     */
    @Schema(description = "本次对话内容")
    private ContentInputInfoDTO dialogueInput;

    @Data
    @Schema(description = "对话输入信息")
    public static class ContentInputInfoDTO {

        /**
         * 对话内容
         */
        @Schema(description = "对话内容")
        private String content;

        /**
         * 提示词
         */
        @Schema(description = "提示词")
        private String prompt_code;

        /**
         * 注：2019-10-12T14:20:50.52+08:00
         */
        @Schema(description = "输入时间")
        private String inputTime;

        /**
         * 意图指令
         */
        @Schema(description = "意图指令")
        private String command;
    }
}