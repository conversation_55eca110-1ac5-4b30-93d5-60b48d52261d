package org.rc.platform.testagent.chat.handler.impl;

import lombok.extern.slf4j.Slf4j;
import org.rc.platform.testagent.chat.dto.ChatHandleDTO;
import org.rc.platform.testagent.chat.handler.AbstractChatHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 参数校验处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ValidationHandler extends AbstractChatHandler {

    @Override
    public int order() {
        return 100;
    }

    @Override
    public boolean execute(ChatHandleDTO handleDTO) {
        // 所有请求都需要参数校验
        return true;
    }

    @Override
    protected boolean doRun(ChatHandleDTO handleDTO) {
        log.info("进入参数校验处理器");

        // 校验请求参数
        if (handleDTO.getReqDTO() == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        // 校验对话内容
        if (handleDTO.getReqDTO().getDialogueInput() == null
            || !StringUtils.hasText(handleDTO.getReqDTO().getDialogueInput().getContent())) {
            throw new IllegalArgumentException("对话内容不能为空");
        }

        // 校验用户ID
        if (!StringUtils.hasText(handleDTO.getReqDTO().getUserId())) {
            log.warn("用户ID为空，使用默认值");
            handleDTO.getReqDTO().setUserId("1222477271620092911");
        }

        // 校验模型类型
        if (!StringUtils.hasText(handleDTO.getReqDTO().getModelType())) {
            log.warn("模型类型为空，使用默认值");
            handleDTO.getReqDTO().setModelType("default");
        }

        log.info("参数校验通过");
        return true; // 继续执行下一个处理器
    }
}
